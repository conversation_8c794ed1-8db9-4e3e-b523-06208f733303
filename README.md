# YOLOv8 手势数字识别系统

基于YOLOv8和MediaPipe的实时手势数字识别系统，能够识别0-9的手势数字。

## 功能特性

1. 实时读取摄像头视频并显示
2. 识别手掌关键节点并连线显示
3. 识别手势表示的数字并在控制台输出

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python main.py
```

## 使用说明

- 启动程序后会打开摄像头窗口
- 将手掌放在摄像头前，系统会自动检测手势
- 识别到数字手势时会在控制台输出对应的阿拉伯数字
- 按 'q' 键退出程序

## 支持的手势

- 0: 握拳
- 1: 竖起食指
- 2: 竖起食指和中指
- 3: 竖起食指、中指和无名指
- 4: 竖起四根手指（除拇指）
- 5: 张开五根手指
- 6-9: 其他特定手势组合
