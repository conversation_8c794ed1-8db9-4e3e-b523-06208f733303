import numpy as np
from hand_detector import HandDetector


class GestureRecognizer:
    """
    基于手指关键点位置实现0-9数字手势的识别
    """
    
    def __init__(self):
        """
        初始化手势识别器
        """
        self.hand_detector = HandDetector()
        
        # 手指索引
        self.finger_tips = [4, 8, 12, 16, 20]  # 拇指、食指、中指、无名指、小指的指尖
        self.finger_pips = [3, 6, 10, 14, 18]  # 手指的第二关节
        self.finger_mcp = [2, 5, 9, 13, 17]   # 手指的根部关节
        
        # 用于稳定识别结果
        self.recognitionq_history = []
        self.history_length = 5
        
    def is_finger_up(self, landmarks, finger_idx):
        """
        判断指定手指是否伸直
        
        Args:
            landmarks: 手势关键点坐标列表
            finger_idx: 手指索引 (0-4: 拇指到小指)
            
        Returns:
            bool: True表示手指伸直，False表示弯曲
        """
        if len(landmarks) != 21:
            return False
        
        tip_idx = self.finger_tips[finger_idx]
        pip_idx = self.finger_pips[finger_idx]
        
        if finger_idx == 0:  # 拇指特殊处理
            # 拇指：比较x坐标，考虑左右手
            mcp_idx = self.finger_mcp[finger_idx]
            return landmarks[tip_idx][0] > landmarks[mcp_idx][0]
        else:
            # 其他手指：比较y坐标
            return landmarks[tip_idx][1] < landmarks[pip_idx][1]
    
    def get_fingers_up(self, landmarks):
        """
        获取所有手指的状态
        
        Args:
            landmarks: 手势关键点坐标列表
            
        Returns:
            list: 手指状态列表 [拇指, 食指, 中指, 无名指, 小指]
        """
        fingers_up = []
        for i in range(5):
            fingers_up.append(self.is_finger_up(landmarks, i))
        return fingers_up
    
    def recognize_number_0(self, landmarks):
        """识别数字0：握拳"""
        fingers_up = self.get_fingers_up(landmarks)
        return sum(fingers_up) == 0
    
    def recognize_number_1(self, landmarks):
        """识别数字1：竖起食指"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [False, True, False, False, False]
    
    def recognize_number_2(self, landmarks):
        """识别数字2：竖起食指和中指"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [False, True, True, False, False]
    
    def recognize_number_3(self, landmarks):
        """识别数字3：竖起食指、中指和无名指"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [False, True, True, True, False]
    
    def recognize_number_4(self, landmarks):
        """识别数字4：竖起四根手指（除拇指）"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [False, True, True, True, True]
    
    def recognize_number_5(self, landmarks):
        """识别数字5：张开五根手指"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [True, True, True, True, True]
    
    def recognize_number_6(self, landmarks):
        """识别数字6：竖起拇指和小指"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [True, False, False, False, True]
    
    def recognize_number_7(self, landmarks):
        """识别数字7：竖起拇指、食指和中指"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [True, True, True, False, False]
    
    def recognize_number_8(self, landmarks):
        """识别数字8：竖起拇指、食指、中指和无名指"""
        fingers_up = self.get_fingers_up(landmarks)
        return fingers_up == [True, True, True, True, False]
    
    def recognize_number_9(self, landmarks):
        """识别数字9：竖起拇指、食指、中指、无名指和小指（与5相同，可以通过其他特征区分）"""
        fingers_up = self.get_fingers_up(landmarks)
        # 这里可以添加更复杂的逻辑来区分5和9
        # 暂时使用简单的逻辑：检查手指间的角度或距离
        if fingers_up == [True, True, True, True, True]:
            # 可以通过检查拇指和食指的角度来区分
            thumb_tip = landmarks[4]
            index_tip = landmarks[8]
            distance = np.sqrt((thumb_tip[0] - index_tip[0])**2 + (thumb_tip[1] - index_tip[1])**2)
            # 如果拇指和食指距离较近，可能是9
            return distance < 50  # 这个阈值可以调整
        return False
    
    def recognize_gesture(self, landmarks):
        """
        识别手势数字
        
        Args:
            landmarks: 手势关键点坐标列表
            
        Returns:
            int: 识别的数字 (0-9)，-1表示未识别
        """
        if len(landmarks) != 21:
            return -1
        
        # 按顺序检测数字
        recognition_functions = [
            self.recognize_number_0,
            self.recognize_number_1,
            self.recognize_number_2,
            self.recognize_number_3,
            self.recognize_number_4,
            self.recognize_number_5,
            self.recognize_number_6,
            self.recognize_number_7,
            self.recognize_number_8,
            self.recognize_number_9
        ]
        
        for i, func in enumerate(recognition_functions):
            if func(landmarks):
                return i
        
        return -1
    
    def recognize_with_stability(self, landmarks):
        """
        带稳定性的手势识别
        
        Args:
            landmarks: 手势关键点坐标列表
            
        Returns:
            int: 稳定识别的数字 (0-9)，-1表示未识别
        """
        current_gesture = self.recognize_gesture(landmarks)
        
        # 添加到历史记录
        self.recognition_history.append(current_gesture)
        
        # 保持历史记录长度
        if len(self.recognition_history) > self.history_length:
            self.recognition_history.pop(0)
        
        # 如果历史记录不够长，返回当前识别结果
        if len(self.recognition_history) < self.history_length:
            return current_gesture
        
        # 统计历史记录中最频繁的手势
        gesture_counts = {}
        for gesture in self.recognition_history:
            if gesture != -1:  # 忽略未识别的结果
                gesture_counts[gesture] = gesture_counts.get(gesture, 0) + 1
        
        if not gesture_counts:
            return -1
        
        # 返回最频繁的手势
        most_frequent_gesture = max(gesture_counts, key=gesture_counts.get)
        
        # 只有当最频繁的手势出现次数超过阈值时才返回
        if gesture_counts[most_frequent_gesture] >= self.history_length // 2 + 1:
            return most_frequent_gesture
        
        return -1
    
    def get_gesture_name(self, gesture_number):
        """
        获取手势名称
        
        Args:
            gesture_number: 手势数字
            
        Returns:
            str: 手势名称
        """
        if gesture_number == -1:
            return "Unknown"
        elif 0 <= gesture_number <= 9:
            return str(gesture_number)
        else:
            return "Invalid"
