#!/usr/bin/env python3
"""
YOLOv8 手势数字识别系统
使用MediaPipe进行手势检测和数字识别

作者: AI Assistant
日期: 2025-08-08
"""

import sys
import argparse
from video_processor import VideoProcessor


def parse_arguments():
    """
    解析命令行参数
    
    Returns:
        args: 解析后的参数
    """
    parser = argparse.ArgumentParser(
        description="YOLOv8 手势数字识别系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 使用默认摄像头
  python main.py --camera 1         # 使用摄像头1
  python main.py --help             # 显示帮助信息

支持的手势:
  0: 握拳
  1: 竖起食指
  2: 竖起食指和中指
  3: 竖起食指、中指和无名指
  4: 竖起四根手指（除拇指）
  5: 张开五根手指
  6: 竖起拇指和小指
  7: 竖起拇指、食指和中指
  8: 竖起拇指、食指、中指和无名指
  9: 特殊的五指张开手势

操作说明:
  - 将手掌放在摄像头前
  - 系统会自动检测手势并显示关键点连线
  - 识别到数字手势时会在控制台输出对应数字
  - 按 'q' 键或 ESC 键退出程序
        """
    )
    
    parser.add_argument(
        '--camera', '-c',
        type=int,
        default=0,
        help='摄像头ID (默认: 0)'
    )
    
    parser.add_argument(
        '--window-name', '-w',
        type=str,
        default="YOLOv8 Hand Gesture Recognition",
        help='显示窗口名称 (默认: "YOLOv8 Hand Gesture Recognition")'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='YOLOv8 手势数字识别系统 v1.0'
    )
    
    return parser.parse_args()


def check_dependencies():
    """
    检查必要的依赖包
    
    Returns:
        bool: 依赖检查是否通过
    """
    required_packages = [
        ('cv2', 'opencv-python'),
        ('mediapipe', 'mediapipe'),
        ('numpy', 'numpy')
    ]
    
    missing_packages = []
    
    for package_name, install_name in required_packages:
        try:
            __import__(package_name)
        except ImportError:
            missing_packages.append(install_name)
    
    if missing_packages:
        print("错误：缺少必要的依赖包")
        print("请运行以下命令安装：")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def print_system_info():
    """打印系统信息"""
    print("=" * 60)
    print("YOLOv8 手势数字识别系统")
    print("=" * 60)
    print("基于MediaPipe的实时手势识别")
    print("支持0-9数字手势识别")
    print("=" * 60)


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_arguments()
    
    # 打印系统信息
    print_system_info()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # 创建视频处理器
        processor = VideoProcessor(
            camera_id=args.camera,
            window_name=args.window_name
        )
        
        # 运行手势识别系统
        processor.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        sys.exit(1)
    
    print("程序正常退出")


if __name__ == "__main__":
    main()
