import cv2
import time
import numpy as np
from hand_detector import HandDetector
from gesture_recognizer import GestureRecognizer


class VideoProcessor:
    """
    实时视频处理类，负责摄像头捕获、手势检测和显示
    """
    
    def __init__(self, camera_id=0, window_name="Hand Gesture Recognition"):
        """
        初始化视频处理器
        
        Args:
            camera_id: 摄像头ID
            window_name: 显示窗口名称
        """
        self.camera_id = camera_id
        self.window_name = window_name
        self.cap = None
        
        # 初始化检测器和识别器
        self.hand_detector = HandDetector(max_num_hands=2)  # 只检测一只手
        self.gesture_recognizer = GestureRecognizer()
        
        # 显示相关参数
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 1
        self.font_thickness = 2
        self.text_color = (0, 255, 0)  # 绿色
        self.bg_color = (0, 0, 0)      # 黑色背景
        
        # FPS计算
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        # 手势识别结果
        self.last_recognized_gesture = -1
        self.last_gesture_time = time.time()
        self.gesture_display_duration = 2.0  # 显示手势结果的持续时间（秒）
        
    def initialize_camera(self):
        """
        初始化摄像头
        
        Returns:
            bool: 初始化是否成功
        """
        self.cap = cv2.VideoCapture(self.camera_id)
        
        if not self.cap.isOpened():
            print(f"错误：无法打开摄像头 {self.camera_id}")
            return False
        
        # 设置摄像头参数
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        
        print(f"摄像头初始化成功，分辨率: {int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")
        return True
    
    def release_camera(self):
        """释放摄像头资源"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
    
    def calculate_fps(self):
        """计算FPS"""
        self.fps_counter += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def draw_info_panel(self, image, gesture_number):
        """
        在图像上绘制信息面板
        
        Args:
            image: 输入图像
            gesture_number: 识别的手势数字
            
        Returns:
            image: 绘制后的图像
        """
        height, width = image.shape[:2]
        
        # 绘制半透明背景
        overlay = image.copy()
        cv2.rectangle(overlay, (10, 10), (300, 150), self.bg_color, -1)
        image = cv2.addWeighted(image, 0.7, overlay, 0.3, 0)
        
        # 显示FPS
        fps_text = f"FPS: {self.current_fps}"
        cv2.putText(image, fps_text, (20, 40), self.font, 0.7, self.text_color, 2)
        
        # 显示识别结果
        if gesture_number != -1:
            gesture_text = f"Gesture: {gesture_number}"
            cv2.putText(image, gesture_text, (20, 80), self.font, 1.2, (0, 255, 255), 3)
            self.last_recognized_gesture = gesture_number
            self.last_gesture_time = time.time()
        else:
            # 显示上一次识别的手势（如果在显示时间内）
            current_time = time.time()
            if (current_time - self.last_gesture_time) < self.gesture_display_duration:
                if self.last_recognized_gesture != -1:
                    gesture_text = f"Gesture: {self.last_recognized_gesture}"
                    cv2.putText(image, gesture_text, (20, 80), self.font, 1.2, (0, 255, 255), 3)
            else:
                gesture_text = "Gesture: None"
                cv2.putText(image, gesture_text, (20, 80), self.font, 1.2, (128, 128, 128), 2)
        
        # 显示操作提示
        help_text = "Press 'q' to quit"
        cv2.putText(image, help_text, (20, 120), self.font, 0.5, (255, 255, 255), 1)
        
        return image
    
    def process_frame(self, frame):
        """
        处理单帧图像
        
        Args:
            frame: 输入帧
            
        Returns:
            processed_frame: 处理后的帧
            gesture_number: 识别的手势数字
        """
        # 水平翻转图像（镜像效果）
        frame = cv2.flip(frame, 1)
        
        # 检测手势
        results, _ = self.hand_detector.detect_hands(frame)
        
        # 绘制手势关键点
        frame = self.hand_detector.draw_landmarks(frame, results)
        
        # 识别手势数字
        gesture_number = -1
        if results.multi_hand_landmarks:
            # 获取第一只手的关键点
            landmarks_list = self.hand_detector.get_landmarks_list(results, frame.shape)
            if landmarks_list:
                landmarks = landmarks_list[0]  # 只处理第一只手
                gesture_number = self.gesture_recognizer.recognize_with_stability(landmarks)
        
        return frame, gesture_number
    
    def run(self):
        """
        运行实时手势识别
        """
        if not self.initialize_camera():
            return
        
        print("手势识别系统启动成功！")
        print("支持的手势：")
        print("0: 握拳")
        print("1: 竖起食指")
        print("2: 竖起食指和中指")
        print("3: 竖起食指、中指和无名指")
        print("4: 竖起四根手指（除拇指）")
        print("5: 张开五根手指")
        print("6: 竖起拇指和小指")
        print("7: 竖起拇指、食指和中指")
        print("8: 竖起拇指、食指、中指和无名指")
        print("9: 特殊的五指张开手势")
        print("\n按 'q' 键退出程序")
        
        try:
            while True:
                # 读取帧
                ret, frame = self.cap.read()
                if not ret:
                    print("错误：无法读取摄像头帧")
                    break
                
                # 处理帧
                processed_frame, gesture_number = self.process_frame(frame)
                
                # 绘制信息面板
                processed_frame = self.draw_info_panel(processed_frame, gesture_number)
                
                # 如果识别到手势，在控制台输出
                if gesture_number != -1:
                    current_time = time.time()
                    # 避免重复输出同一个手势
                    if (gesture_number != self.last_recognized_gesture or 
                        current_time - self.last_gesture_time > 1.0):
                        print(f"识别到数字: {gesture_number}")
                
                # 计算FPS
                self.calculate_fps()
                
                # 显示图像
                cv2.imshow(self.window_name, processed_frame)
                
                # 检查退出条件
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == 27:  # 'q'键或ESC键
                    break
                    
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"运行时错误: {e}")
        finally:
            self.release_camera()
            print("程序已退出")
