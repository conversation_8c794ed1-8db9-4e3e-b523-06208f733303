import cv2
import mediapipe as mp
import numpy as np


class HandDetector:
    """
    使用MediaPipe实现手势关键点检测和连线绘制
    """
    
    def __init__(self, max_num_hands=2, min_detection_confidence=0.7, min_tracking_confidence=0.5):
        """
        初始化手势检测器
        
        Args:
            max_num_hands: 最大检测手数
            min_detection_confidence: 最小检测置信度
            min_tracking_confidence: 最小跟踪置信度
        """
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=max_num_hands,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # 手指关键点索引
        self.finger_tips = [4, 8, 12, 16, 20]  # 拇指、食指、中指、无名指、小指的指尖
        self.finger_pips = [3, 6, 10, 14, 18]  # 手指的第二关节
        
    def detect_hands(self, image):
        """
        检测手势关键点
        
        Args:
            image: 输入图像
            
        Returns:
            results: MediaPipe检测结果
            image_rgb: RGB格式图像
        """
        # 转换为RGB格式
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image_rgb.flags.writeable = False
        
        # 检测手势
        results = self.hands.process(image_rgb)
        
        # 恢复可写状态
        image_rgb.flags.writeable = True
        
        return results, image_rgb
    
    def draw_landmarks(self, image, results):
        """
        在图像上绘制手势关键点和连线
        
        Args:
            image: 输入图像
            results: MediaPipe检测结果
            
        Returns:
            image: 绘制后的图像
        """
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # 绘制手势关键点和连线
                self.mp_drawing.draw_landmarks(
                    image,
                    hand_landmarks,
                    self.mp_hands.HAND_CONNECTIONS,
                    self.mp_drawing_styles.get_default_hand_landmarks_style(),
                    self.mp_drawing_styles.get_default_hand_connections_style()
                )
        
        return image
    
    def get_landmarks_list(self, results, image_shape):
        """
        获取手势关键点坐标列表
        
        Args:
            results: MediaPipe检测结果
            image_shape: 图像尺寸 (height, width, channels)
            
        Returns:
            landmarks_list: 关键点坐标列表
        """
        landmarks_list = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                landmarks = []
                for landmark in hand_landmarks.landmark:
                    # 转换为像素坐标
                    x = int(landmark.x * image_shape[1])
                    y = int(landmark.y * image_shape[0])
                    landmarks.append([x, y])
                landmarks_list.append(landmarks)
        
        return landmarks_list
    
    def get_finger_status(self, landmarks):
        """
        获取手指状态（伸直或弯曲）
        
        Args:
            landmarks: 单个手的关键点坐标列表
            
        Returns:
            fingers_up: 手指状态列表，1表示伸直，0表示弯曲
        """
        fingers_up = []
        
        if len(landmarks) != 21:
            return [0, 0, 0, 0, 0]
        
        # 拇指：比较x坐标（左右手需要区分）
        if landmarks[self.finger_tips[0]][0] > landmarks[self.finger_tips[0] - 1][0]:
            fingers_up.append(1)
        else:
            fingers_up.append(0)
        
        # 其他四指：比较y坐标
        for i in range(1, 5):
            if landmarks[self.finger_tips[i]][1] < landmarks[self.finger_pips[i]][1]:
                fingers_up.append(1)
            else:
                fingers_up.append(0)
        
        return fingers_up
    
    def count_fingers(self, landmarks):
        """
        计算伸直的手指数量
        
        Args:
            landmarks: 单个手的关键点坐标列表
            
        Returns:
            count: 伸直的手指数量
        """
        fingers_up = self.get_finger_status(landmarks)
        return sum(fingers_up)
    
    def get_hand_center(self, landmarks):
        """
        获取手掌中心点坐标
        
        Args:
            landmarks: 单个手的关键点坐标列表
            
        Returns:
            center: 手掌中心点坐标 (x, y)
        """
        if len(landmarks) == 0:
            return (0, 0)
        
        # 使用手腕点作为参考
        wrist = landmarks[0]
        middle_mcp = landmarks[9]  # 中指根部
        
        center_x = (wrist[0] + middle_mcp[0]) // 2
        center_y = (wrist[1] + middle_mcp[1]) // 2
        
        return (center_x, center_y)
