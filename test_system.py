#!/usr/bin/env python3
"""
测试手势识别系统的各个模块
"""

import numpy as np
from hand_detector import HandDetector
from gesture_recognizer import GestureRecognizer


def test_hand_detector():
    """测试手势检测器"""
    print("测试手势检测器...")
    
    detector = HandDetector()
    print(f"✓ HandDetector 初始化成功")
    print(f"✓ 手指关键点索引: {detector.finger_tips}")
    print(f"✓ 手指关节索引: {detector.finger_pips}")
    
    return True


def test_gesture_recognizer():
    """测试手势识别器"""
    print("\n测试手势识别器...")
    
    recognizer = GestureRecognizer()
    print(f"✓ GestureRecognizer 初始化成功")
    
    # 创建模拟的手势关键点数据
    # 这里创建一些简单的测试数据
    
    # 测试数字1的手势（只有食指伸直）
    landmarks_1 = [[100, 200] for _ in range(21)]  # 初始化21个关键点
    # 设置食指伸直的状态
    landmarks_1[8] = [120, 150]  # 食指指尖
    landmarks_1[6] = [120, 180]  # 食指第二关节
    landmarks_1[5] = [120, 200]  # 食指根部
    
    # 其他手指弯曲
    landmarks_1[4] = [90, 200]   # 拇指
    landmarks_1[12] = [130, 210] # 中指
    landmarks_1[16] = [140, 210] # 无名指
    landmarks_1[20] = [150, 210] # 小指
    
    gesture = recognizer.recognize_gesture(landmarks_1)
    print(f"✓ 测试手势识别: 模拟数字1手势，识别结果: {gesture}")
    
    # 测试手势名称获取
    for i in range(10):
        name = recognizer.get_gesture_name(i)
        print(f"  数字 {i}: {name}")
    
    print(f"  未知手势: {recognizer.get_gesture_name(-1)}")
    
    return True


def test_finger_detection():
    """测试手指状态检测"""
    print("\n测试手指状态检测...")
    
    recognizer = GestureRecognizer()
    
    # 创建模拟的手势数据
    landmarks = [[100, 200] for _ in range(21)]
    
    # 测试各种手指状态
    test_cases = [
        ("握拳", [False, False, False, False, False]),
        ("竖起食指", [False, True, False, False, False]),
        ("比二", [False, True, True, False, False]),
        ("张开手掌", [True, True, True, True, True])
    ]
    
    for gesture_name, expected_fingers in test_cases:
        # 根据期望的手指状态设置关键点
        for i, is_up in enumerate(expected_fingers):
            tip_idx = recognizer.finger_tips[i]
            pip_idx = recognizer.finger_pips[i]
            
            if is_up:
                landmarks[tip_idx] = [100 + i*10, 150]  # 指尖在上方
                landmarks[pip_idx] = [100 + i*10, 180]  # 关节在下方
            else:
                landmarks[tip_idx] = [100 + i*10, 210]  # 指尖在下方
                landmarks[pip_idx] = [100 + i*10, 180]  # 关节在上方
        
        detected_fingers = recognizer.get_fingers_up(landmarks)
        print(f"  {gesture_name}: 期望 {expected_fingers}, 检测到 {detected_fingers}")
    
    return True


def test_system_integration():
    """测试系统集成"""
    print("\n测试系统集成...")
    
    try:
        from video_processor import VideoProcessor
        print("✓ VideoProcessor 导入成功")
        
        # 测试初始化（不启动摄像头）
        processor = VideoProcessor(camera_id=-1)  # 使用无效的摄像头ID
        print("✓ VideoProcessor 初始化成功")
        
        return True
    except Exception as e:
        print(f"✗ 系统集成测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("YOLOv8 手势识别系统 - 模块测试")
    print("=" * 60)
    
    tests = [
        ("手势检测器", test_hand_detector),
        ("手势识别器", test_gesture_recognizer),
        ("手指状态检测", test_finger_detection),
        ("系统集成", test_system_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"\n✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"\n✗ {test_name} 测试失败")
        except Exception as e:
            print(f"\n✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        print("\n使用方法:")
        print("  python main.py              # 启动手势识别系统")
        print("  python main.py --help       # 查看帮助信息")
        print("  python main.py --camera 1   # 使用摄像头1")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
    
    print("=" * 60)


if __name__ == "__main__":
    run_all_tests()
