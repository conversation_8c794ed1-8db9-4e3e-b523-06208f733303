#!/usr/bin/env python3
"""
YOLOv8 手势数字识别系统演示脚本
展示如何使用各个模块进行手势识别
"""

import cv2
import numpy as np
from hand_detector import HandDetector
from gesture_recognizer import GestureRecognizer


def create_demo_image():
    """创建演示图像"""
    # 创建一个空白图像
    img = np.zeros((600, 800, 3), dtype=np.uint8)
    
    # 添加标题
    cv2.putText(img, "YOLOv8 Hand Gesture Recognition Demo", 
                (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # 添加说明文字
    instructions = [
        "Supported Gestures:",
        "0: Fist (握拳)",
        "1: Index finger up (竖起食指)",
        "2: Index + Middle fingers (食指+中指)",
        "3: Index + Middle + Ring fingers (食指+中指+无名指)",
        "4: Four fingers up (四根手指)",
        "5: All fingers up (张开手掌)",
        "6: Thumb + Pinky (拇指+小指)",
        "7: Thumb + Index + Middle (拇指+食指+中指)",
        "8: Thumb + Index + Middle + Ring (拇指+食指+中指+无名指)",
        "9: Special five-finger gesture (特殊五指手势)",
        "",
        "Instructions:",
        "- Place your hand in front of the camera",
        "- The system will detect hand landmarks and draw connections",
        "- Recognized numbers will be displayed in console",
        "- Press 'q' to quit"
    ]
    
    y_offset = 100
    for instruction in instructions:
        cv2.putText(img, instruction, (50, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
        y_offset += 30
    
    return img


def demo_without_camera():
    """无摄像头演示模式"""
    print("=" * 60)
    print("YOLOv8 手势识别系统 - 演示模式")
    print("=" * 60)
    print("由于没有检测到摄像头或选择演示模式，将显示系统功能介绍")
    
    # 创建演示图像
    demo_img = create_demo_image()
    
    # 显示演示图像
    cv2.imshow("YOLOv8 Hand Gesture Recognition - Demo", demo_img)
    
    print("\n系统功能演示:")
    print("1. 手势检测器初始化...")
    detector = HandDetector()
    print("   ✓ MediaPipe手势检测器已初始化")
    
    print("2. 手势识别器初始化...")
    recognizer = GestureRecognizer()
    print("   ✓ 数字手势识别器已初始化")
    
    print("3. 支持的手势类型:")
    gestures = {
        0: "握拳 (Fist)",
        1: "竖起食指 (Index finger)",
        2: "比二 (Peace sign)",
        3: "比三 (Three fingers)",
        4: "比四 (Four fingers)",
        5: "张开手掌 (Open hand)",
        6: "拇指+小指 (Thumb + Pinky)",
        7: "拇指+食指+中指 (Thumb + Index + Middle)",
        8: "拇指+食指+中指+无名指 (Four fingers with thumb)",
        9: "特殊五指手势 (Special five-finger gesture)"
    }
    
    for num, desc in gestures.items():
        print(f"   {num}: {desc}")
    
    print("\n4. 系统特性:")
    print("   ✓ 实时手势检测和识别")
    print("   ✓ 手掌关键点可视化")
    print("   ✓ 稳定的识别算法")
    print("   ✓ 控制台数字输出")
    print("   ✓ FPS显示")
    
    print("\n按任意键关闭演示窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    print("\n要启动实际的手势识别系统，请运行:")
    print("  python main.py")


def demo_with_camera():
    """带摄像头的演示模式"""
    print("启动摄像头演示模式...")
    
    # 尝试初始化摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("无法打开摄像头，切换到无摄像头演示模式")
        demo_without_camera()
        return
    
    # 初始化检测器
    detector = HandDetector()
    recognizer = GestureRecognizer()
    
    print("摄像头演示启动成功！")
    print("将手掌放在摄像头前进行手势识别")
    print("按 'q' 键退出演示")
    
    frame_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 水平翻转
            frame = cv2.flip(frame, 1)
            
            # 检测手势
            results, _ = detector.detect_hands(frame)
            
            # 绘制关键点
            frame = detector.draw_landmarks(frame, results)
            
            # 识别手势
            if results.multi_hand_landmarks:
                landmarks_list = detector.get_landmarks_list(results, frame.shape)
                if landmarks_list:
                    landmarks = landmarks_list[0]
                    gesture_num = recognizer.recognize_gesture(landmarks)
                    
                    if gesture_num != -1:
                        # 在图像上显示识别结果
                        cv2.putText(frame, f"Gesture: {gesture_num}", 
                                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                        
                        # 每30帧输出一次结果（避免过于频繁）
                        if frame_count % 30 == 0:
                            print(f"识别到数字: {gesture_num}")
            
            # 添加说明文字
            cv2.putText(frame, "Press 'q' to quit", 
                       (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 显示图像
            cv2.imshow("YOLOv8 Hand Gesture Recognition - Camera Demo", frame)
            
            # 检查退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    finally:
        cap.release()
        cv2.destroyAllWindows()
        print("摄像头演示结束")


def main():
    """主函数"""
    print("YOLOv8 手势数字识别系统 - 演示脚本")
    print("选择演示模式:")
    print("1. 摄像头演示 (需要摄像头)")
    print("2. 功能介绍演示 (无需摄像头)")
    print("3. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-3): ").strip()
            
            if choice == '1':
                demo_with_camera()
                break
            elif choice == '2':
                demo_without_camera()
                break
            elif choice == '3':
                print("退出演示")
                break
            else:
                print("无效选择，请输入 1、2 或 3")
                
        except KeyboardInterrupt:
            print("\n\n演示被用户中断")
            break
        except Exception as e:
            print(f"演示过程中出现错误: {e}")
            break


if __name__ == "__main__":
    main()
